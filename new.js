const { createClient } = require('@supabase/supabase-js');

// Configuration
const SUPABASE_URL = process.env.SUPABASE_URL || 'your-supabase-url';
const SUPABASE_ANON_KEY = process.env.SUPABASE_ANON_KEY || 'your-supabase-anon-key';

// Initialize Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

/**
 * Generates a magic link for user authentication
 * @param {string} email - User's email address
 * @param {Object} options - Additional options for magic link generation
 * @param {string} options.redirectTo - URL to redirect after clicking magic link
 * @param {Object} options.data - Additional user metadata
 * @returns {Promise<Object>} Response object with success/error status
 */
async function generateMagicLink(email, options = {}) {
  try {
    // Validate email
    if (!email || !isValidEmail(email)) {
      throw new Error('Valid email address is required');
    }

    // Prepare magic link options
    const magicLinkOptions = {
      email: email,
      options: {
        emailRedirectTo: options.redirectTo || `${process.env.APP_URL || 'http://localhost:3000'}/auth/callback`,
        data: options.data || {}
      }
    };

    console.log(`Generating magic link for: ${email}`);
    
    // Generate magic link
    const { data, error } = await supabase.auth.signInWithOtp(magicLinkOptions);

    if (error) {
      console.error('Error generating magic link:', error.message);
      return {
        success: false,
        error: error.message,
        code: error.status
      };
    }

    console.log('Magic link sent successfully!');
    return {
      success: true,
      message: 'Magic link sent to email',
      data: {
        email: email,
        user: data.user,
        session: data.session
      }
    };

  } catch (error) {
    console.error('Unexpected error:', error.message);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Validates email format
 * @param {string} email - Email to validate
 * @returns {boolean} True if email is valid
 */
function isValidEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * Batch generate magic links for multiple users
 * @param {Array<string>} emails - Array of email addresses
 * @param {Object} options - Options for magic link generation
 * @returns {Promise<Array>} Array of results for each email
 */
async function generateBulkMagicLinks(emails, options = {}) {
  const results = [];
  
  for (const email of emails) {
    const result = await generateMagicLink(email, options);
    results.push({
      email,
      ...result
    });
    
    // Add delay to avoid rate limiting
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  return results;
}

/**
 * CLI interface for the script
 */
async function main() {
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    console.log(`
Usage: node magic-link-generator.js <email> [redirectUrl]

Examples:
  node magic-link-generator.js <EMAIL>
  node magic-link-generator.js <EMAIL> https://myapp.com/dashboard
  
Environment variables required:
  SUPABASE_URL - Your Supabase project URL
  SUPABASE_ANON_KEY - Your Supabase anonymous key
  APP_URL - Your application URL (optional, defaults to http://localhost:3000)
    `);
    process.exit(1);
  }

  const email = args[0];
  const redirectTo = args[1];
  
  console.log('Supabase Magic Link Generator');
  console.log('============================');
  
  const result = await generateMagicLink(email, { redirectTo });
  
  if (result.success) {
    console.log('✅ Success!');
    console.log(`Magic link sent to: ${email}`);
    if (redirectTo) {
      console.log(`Redirect URL: ${redirectTo}`);
    }
  } else {
    console.log('❌ Failed!');
    console.log(`Error: ${result.error}`);
    process.exit(1);
  }
}

// Export functions for use as module
module.exports = {
  generateMagicLink,
  generateBulkMagicLinks,
  isValidEmail
};

// Run CLI if this file is executed directly
if (require.main === module) {
  main().catch(console.error);
}